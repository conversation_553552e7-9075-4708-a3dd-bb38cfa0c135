"""
Message processor for categorizing and handling different types of Telegram messages
"""
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import json

from src.telegram_client import TelegramMessage, TelegramClient
from src.config import Config
from src.utils.logger import logger

class ProcessedMessage:
    """Represents a message that has been processed and is ready for video generation"""
    
    def __init__(self, telegram_message: TelegramMessage, media_paths: List[Path] = None):
        self.telegram_message = telegram_message
        self.media_paths = media_paths or []
        self.processing_timestamp = datetime.now()
        self.video_generated = False
        self.instagram_posted = False
        self.output_video_path = None
    
    @property
    def content_type(self) -> str:
        return self.telegram_message.content_type
    
    @property
    def text_content(self) -> str:
        """Get the text content for the message"""
        text = self.telegram_message.text or self.telegram_message.caption

        # Remove @linkychannel from the end of messages
        if text:
            # Remove @linkychannel (case insensitive) from end
            import re
            text = re.sub(r'\s*@linkychannel\s*$', '', text, flags=re.IGNORECASE)
            text = text.strip()

        return text
    
    @property
    def should_skip(self) -> bool:
        """Check if this message should be skipped"""
        return self.telegram_message.is_v2ray_config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return {
            'telegram_message': self.telegram_message.to_dict(),
            'media_paths': [str(path) for path in self.media_paths],
            'processing_timestamp': self.processing_timestamp.isoformat(),
            'video_generated': self.video_generated,
            'instagram_posted': self.instagram_posted,
            'output_video_path': str(self.output_video_path) if self.output_video_path else None,
            'content_type': self.content_type,
            'should_skip': self.should_skip
        }

class MessageProcessor:
    """Processes Telegram messages and prepares them for video generation"""
    
    def __init__(self):
        self.telegram_client = TelegramClient()
        self.processing_queue_file = Config.BASE_DIR / "processing_queue.json"
        self.processing_queue = self._load_processing_queue()
    
    def _load_processing_queue(self) -> List[ProcessedMessage]:
        """Load the processing queue from file"""
        if self.processing_queue_file.exists():
            try:
                with open(self.processing_queue_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    queue = []
                    for item in data.get('queue', []):
                        # Reconstruct ProcessedMessage objects
                        telegram_msg = TelegramMessage(item['telegram_message'])
                        media_paths = [Path(path) for path in item['media_paths']]
                        processed_msg = ProcessedMessage(telegram_msg, media_paths)
                        processed_msg.processing_timestamp = datetime.fromisoformat(item['processing_timestamp'])
                        processed_msg.video_generated = item['video_generated']
                        processed_msg.instagram_posted = item['instagram_posted']
                        processed_msg.output_video_path = Path(item['output_video_path']) if item['output_video_path'] else None
                        queue.append(processed_msg)
                    return queue
            except Exception as e:
                logger.warning(f"Could not load processing queue: {e}")
        return []
    
    def _save_processing_queue(self):
        """Save the processing queue to file"""
        try:
            data = {
                'queue': [msg.to_dict() for msg in self.processing_queue],
                'last_updated': datetime.now().isoformat()
            }
            with open(self.processing_queue_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Could not save processing queue: {e}")
    
    async def poll_and_process_messages(self) -> List[ProcessedMessage]:
        """
        Poll Telegram for new messages and process them

        Returns:
            List of ProcessedMessage objects ready for video generation
        """
        logger.info("Polling Telegram for new messages...")

        # Initialize Telegram client
        if not await self.telegram_client.initialize():
            logger.error("Failed to initialize Telegram client")
            return []

        try:
            # Get recent messages
            messages = await self.telegram_client.get_recent_messages(
                limit=Config.MAX_MESSAGES_TO_PROCESS
            )

            new_processed_messages = []

            for message in messages:
                logger.info(f"Processing message {message.message_id}: {message.content_type}")

                # Skip V2ray config messages
                if message.is_v2ray_config:
                    logger.info(f"Skipping V2ray config message {message.message_id}")
                    continue

                # Check if we already have this message in our processing queue
                existing_message = self._find_message_in_queue(message.message_id)
                if existing_message:
                    if existing_message.video_generated:
                        logger.info(f"Message {message.message_id} already has video generated, skipping")
                        continue
                    else:
                        logger.info(f"Message {message.message_id} already in queue but no video yet, will process")
                        new_processed_messages.append(existing_message)
                        continue

                # Download media files if present
                media_paths = []
                if message.media_files:
                    media_paths = await self._download_message_media(message)

                # Create processed message
                processed_message = ProcessedMessage(message, media_paths)
                new_processed_messages.append(processed_message)
                self.processing_queue.append(processed_message)

                logger.info(f"Added message {message.message_id} to processing queue")

            # Save updated queue
            self._save_processing_queue()

            logger.info(f"Processed {len(new_processed_messages)} new messages")
            return new_processed_messages

        except Exception as e:
            logger.error(f"Error polling and processing messages: {e}")
            return []

        finally:
            await self.telegram_client.close()
    
    async def _download_message_media(self, message: TelegramMessage) -> List[Path]:
        """Download media files for a message"""
        media_paths = []
        
        for media_file in message.media_files:
            try:
                # Create download directory for this message
                download_dir = Config.TEMP_DIR / f"message_{message.message_id}"
                download_dir.mkdir(exist_ok=True)
                
                # Download the media file
                media_path = await self.telegram_client.download_media(media_file, download_dir)
                if media_path:
                    media_paths.append(media_path)
                    logger.info(f"Downloaded media: {media_path}")
                
            except Exception as e:
                logger.error(f"Error downloading media file: {e}")
        
        return media_paths

    def _find_message_in_queue(self, message_id: int) -> Optional[ProcessedMessage]:
        """Find a message in the processing queue by message ID"""
        for processed_message in self.processing_queue:
            if processed_message.telegram_message.message_id == message_id:
                return processed_message
        return None

    def get_pending_messages(self, content_type: str = None) -> List[ProcessedMessage]:
        """
        Get messages that are pending video generation
        
        Args:
            content_type: Filter by content type ('text_only', 'text_image', 'video_text')
        
        Returns:
            List of ProcessedMessage objects
        """
        pending = [msg for msg in self.processing_queue 
                  if not msg.video_generated and not msg.should_skip]
        
        if content_type:
            pending = [msg for msg in pending if msg.content_type == content_type]
        
        return pending
    
    def get_ready_for_posting(self) -> List[ProcessedMessage]:
        """Get messages that have videos generated but haven't been posted to Instagram"""
        return [msg for msg in self.processing_queue 
                if msg.video_generated and not msg.instagram_posted and not msg.should_skip]
    
    def mark_video_generated(self, message: ProcessedMessage, video_path: Path):
        """Mark a message as having its video generated"""
        message.video_generated = True
        message.output_video_path = video_path
        self._save_processing_queue()
        logger.info(f"Marked message {message.telegram_message.message_id} as video generated")
    
    def mark_instagram_posted(self, message: ProcessedMessage):
        """Mark a message as posted to Instagram"""
        message.instagram_posted = True
        self._save_processing_queue()
        logger.info(f"Marked message {message.telegram_message.message_id} as posted to Instagram")
    
    def get_statistics(self) -> Dict[str, int]:
        """Get processing statistics"""
        total = len(self.processing_queue)
        skipped = len([msg for msg in self.processing_queue if msg.should_skip])
        video_generated = len([msg for msg in self.processing_queue if msg.video_generated])
        instagram_posted = len([msg for msg in self.processing_queue if msg.instagram_posted])
        pending = total - skipped - video_generated
        
        return {
            'total_messages': total,
            'skipped_messages': skipped,
            'videos_generated': video_generated,
            'instagram_posted': instagram_posted,
            'pending_processing': pending
        }
    
    def cleanup_old_temp_files(self, days_old: int = 7):
        """Clean up old temporary files"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            for temp_dir in Config.TEMP_DIR.iterdir():
                if temp_dir.is_dir():
                    dir_time = temp_dir.stat().st_mtime
                    if dir_time < cutoff_time:
                        import shutil
                        shutil.rmtree(temp_dir)
                        logger.info(f"Cleaned up old temp directory: {temp_dir}")
        
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
